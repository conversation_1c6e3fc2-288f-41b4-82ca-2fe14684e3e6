<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Rust WebRTC Demo</title>
</head>
<body>
  <h2>Rust ↔ Browser WebRTC DataChannel Demo</h2>

  <textarea id="remoteOffer" placeholder="粘贴 Rust 输出的 SDP Offer"></textarea><br>
  <button id="connect">生成 Answer</button><br><br>

  <textarea id="localAnswer" placeholder="浏览器生成的 SDP Answer 会显示在这里"></textarea><br><br>

  <input id="message" placeholder="输入消息" />
  <button id="send">发送</button>

  <pre id="log"></pre>

  <script>
    let pc;
    let channel;

    function log(msg) {
      document.getElementById("log").textContent += msg + "\n";
    }

    document.getElementById("connect").onclick = async () => {
      const offerText = document.getElementById("remoteOffer").value;
      if (!offerText) {
        alert("请先粘贴 Rust 输出的 SDP Offer！");
        return;
      }

      // 创建 PeerConnection，添加 STUN 服务器
      pc = new RTCPeerConnection({
        iceServers: [
          { urls: 'stun:stun.l.google.com:19302' }
        ]
      });

      // 监听 DataChannel
      pc.ondatachannel = (event) => {
        channel = event.channel;
        log("✅ DataChannel opened!");
        channel.onmessage = (ev) => {
          log("📩 Received from Rust: " + ev.data);
        };
      };

      // 清理和标准化 SDP 格式
      let cleanSdp = offerText.trim();

      // 确保每行都以 \r\n 结尾（SDP 标准要求）
      cleanSdp = cleanSdp.replace(/\r\n/g, '\n').replace(/\n/g, '\r\n');

      // 确保 SDP 以 \r\n 结尾
      if (!cleanSdp.endsWith('\r\n')) {
        cleanSdp += '\r\n';
      }

      log("🔍 Processing SDP with " + cleanSdp.split('\r\n').length + " lines");

      try {
        // 设置远端 Offer (来自 Rust)
        await pc.setRemoteDescription({
          type: "offer",
          sdp: cleanSdp
        });
        log("✅ Remote description set successfully");
      } catch (error) {
        log("❌ Error setting remote description: " + error.message);
        console.error("SDP content:", cleanSdp);
        throw error;
      }

      // 创建本地 Answer
      const answer = await pc.createAnswer();
      await pc.setLocalDescription(answer);

      // 输出 Answer，粘贴回 Rust 端
      document.getElementById("localAnswer").value = answer.sdp;
      log("📤 复制上面的 Answer 粘贴到 Rust 程序里");
    };

    // 发送消息到 Rust
    document.getElementById("send").onclick = () => {
      const msg = document.getElementById("message").value;
      if (channel && channel.readyState === "open") {
        channel.send(msg);
        log("📤 Sent to Rust: " + msg);
      } else {
        log("⚠️ DataChannel not open yet!");
      }
    };
  </script>
</body>
</html>
