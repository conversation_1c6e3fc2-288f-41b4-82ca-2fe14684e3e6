use std::sync::Arc;
use webrtc::api::APIBuilder;
use webrtc::data_channel::data_channel_init::RTCDataChannelInit;
use webrtc::peer_connection::configuration::RTCConfiguration;
use webrtc::peer_connection::sdp::session_description::RTCSessionDescription;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化 WebRTC API
    let api = APIBuilder::new().build();

    // 创建 PeerConnection 配置
    let mut config = RTCConfiguration::default();
    // 添加 STUN 服务器以确保 ICE 正常工作
    config.ice_servers = vec![webrtc::ice_transport::ice_server::RTCIceServer {
        urls: vec!["stun:stun.l.google.com:19302".to_string()],
        ..Default::default()
    }];

    let peer_connection = Arc::new(api.new_peer_connection(config).await?);

    // 创建 DataChannel
    let dc_init = RTCDataChannelInit::default();
    let data_channel = peer_connection
        .create_data_channel("demo", Some(dc_init))
        .await?;

    // 设置 DataChannel 的回调
    let dc = data_channel.clone();
    data_channel.on_open(Box::new(move || {
        let dc = dc.clone();
        Box::pin(async move {
            println!("✅ DataChannel opened!");
            dc.send_text("Hello from Rust!".to_string()).await.unwrap();
        })
    }));

    data_channel.on_message(Box::new(move |msg| {
        Box::pin(async move {
            if msg.is_string {
                let text = String::from_utf8_lossy(&msg.data);
                println!("📩 Received message: {}", text);
            } else {
                println!("📩 Received binary data: {:?}", msg.data);
            }
        })
    }));

    // 创建 SDP offer
    let offer = peer_connection.create_offer(None).await?;
    peer_connection.set_local_description(offer.clone()).await?;

    // 打印生成的 SDP offer
    println!("🚀 生成的 SDP Offer:");
    println!("=====================================");
    println!("{}", offer.sdp);
    println!("=====================================");

    // 添加调试信息
    let lines: Vec<&str> = offer.sdp.lines().collect();
    println!("📊 SDP 统计: {} 行", lines.len());

    // 检查是否有问题的 ice-pwd 行
    for (i, line) in lines.iter().enumerate() {
        if line.starts_with("a=ice-pwd:") {
            println!("🔍 第{}行 ice-pwd: {}", i + 1, line);
            println!("   长度: {} 字符", line.len());
            println!("   结尾字符: {:?}", line.chars().last());
        }
    }
    println!();

    // 模拟：你需要把远端返回的 answer 粘贴进来
    println!("请输入远端的 SDP Answer，然后按回车:");
    let mut sdp_answer = String::new();
    std::io::stdin().read_line(&mut sdp_answer)?;

    let answer = RTCSessionDescription::answer(sdp_answer.trim().to_string())?;
    peer_connection.set_remote_description(answer).await?;

    // 阻塞，保持程序运行
    tokio::signal::ctrl_c().await?;
    Ok(())
}
