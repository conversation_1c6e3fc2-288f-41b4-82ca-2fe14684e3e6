use std::sync::Arc;
use tokio::sync::Mutex;
use webrtc::api::APIBuilder;
use webrtc::data_channel::data_channel_init::RTCDataChannelInit;
use webrtc::peer_connection::configuration::RTCConfiguration;
use webrtc::peer_connection::sdp::session_description::RTCSessionDescription;
use webrtc::peer_connection::RTCPeerConnection;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化 WebRTC API
    let api = APIBuilder::new().build();

    // 创建 PeerConnection
    let config = RTCConfiguration::default();
    let peer_connection = Arc::new(api.new_peer_connection(config).await?);

    // 创建 DataChannel
    let dc_init = RTCDataChannelInit::default();
    let data_channel = peer_connection
        .create_data_channel("demo", Some(dc_init))
        .await?;

    // 设置 DataChannel 的回调
    // 设置 DataChannel 的回调
    let dc = data_channel.clone();
    data_channel.on_open(Box::new(move || {
        let dc = dc.clone();
        Box::pin(async move {
            println!("✅ DataChannel opened!");
            dc.send_text("Hello from Rust!".to_string()).await.unwrap();
        })
    }));

    data_channel.on_message(Box::new(move |msg| {
        if let Some(text) = msg.str {
            println!("📩 Received message: {}", text);
        } else {
            println!("📩 Received binary data: {:?}", msg.data);
        }
        Box::pin(async {})
    }));

    // 模拟：你需要把远端返回的 answer 粘贴进来
    println!("请输入远端的 SDP Answer，然后按回车:");
    let mut sdp_answer = String::new();
    std::io::stdin().read_line(&mut sdp_answer)?;

    let answer = RTCSessionDescription::answer(sdp_answer.trim().to_string())?;
    peer_connection.set_remote_description(answer).await?;

    // 阻塞，保持程序运行
    tokio::signal::ctrl_c().await?;
    Ok(())
}
